import { textareaProps } from "@/interfaces/components/input/input";
import View from "./view";

const Textarea: React.FC<textareaProps> = ({
  type = "text",
  onChange,
  onBlur,
  disabled = false,
  placeholder = "Enter text",
  value,
  // defaultValue,
  variant = "default",
  fullWidth = false,
  id,
  name,
  ref,
  style,
  className,
  leftIcon,
  rightIcon,
  error,
  label,
  required = false,
  ...rest
}) => {
  const variantClasses = {
    default: "border border-border bg-background hover:border-primary-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
    filled: "border border-transparent bg-primary-10 dark:bg-primary-900/20 hover:bg-primary-20 dark:hover:bg-primary-900/30 focus:bg-background focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
    outlined: "border border-border bg-transparent hover:border-primary-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
    error: "border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-100 bg-red-50 dark:bg-red-900/20",
  };

  return (
    <View>
      {label && (
        <label htmlFor={name} className="block mb-0.5 text-text-dark">
          {label}
          {required && <span className="text-red-600">*</span>}
        </label>
      )}
      <View className={`input-wrapper ${fullWidth ? "w-full" : ""}`}>
        {leftIcon && <View className="input-icon-left">{leftIcon}</View>}
        <textarea
          ref={ref}
          onChange={onChange}
          onBlur={onBlur}
          disabled={disabled}
          placeholder={placeholder}
          value={value + ""}
          id={id}
          name={name}
          className={`
            w-full p-4 rounded-lg focus:outline-none transition-all duration-200
            placeholder:text-muted-foreground
            disabled:opacity-50 disabled:cursor-not-allowed
            min-h-[120px] resize-y
            ${variantClasses[variant as keyof typeof variantClasses] || variantClasses.default}
            ${error ? variantClasses.error : ""}
            ${className || ""}
          `}
          style={style}
          {...rest}
        ></textarea>
      </View>
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </View>
  );
};
export default Textarea;
