{"version": 3, "file": "InputRule.d.ts", "sourceRoot": "", "sources": ["../src/InputRule.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,EAAiB,MAAM,kBAAkB,CAAA;AAGrE,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAIpC,OAAO,EACL,WAAW,EACX,eAAe,EACf,wBAAwB,EACxB,KAAK,EACL,cAAc,EACf,MAAM,YAAY,CAAA;AAGnB,MAAM,MAAM,cAAc,GAAG;IAC3B,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,CAAC,EAAE,gBAAgB,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,cAAc,GAAG,IAAI,CAAC,CAAC;AAEjF,qBAAa,SAAS;IACpB,IAAI,EAAE,eAAe,CAAA;IAErB,OAAO,EAAE,CAAC,KAAK,EAAE;QACf,KAAK,EAAE,WAAW,CAAC;QACnB,KAAK,EAAE,KAAK,CAAC;QACb,KAAK,EAAE,wBAAwB,CAAC;QAChC,QAAQ,EAAE,cAAc,CAAC;QACzB,KAAK,EAAE,MAAM,eAAe,CAAC;QAC7B,GAAG,EAAE,MAAM,WAAW,CAAC;KACxB,KAAK,IAAI,GAAG,IAAI,CAAA;gBAEL,MAAM,EAAE;QAClB,IAAI,EAAE,eAAe,CAAC;QACtB,OAAO,EAAE,CAAC,KAAK,EAAE;YACf,KAAK,EAAE,WAAW,CAAC;YACnB,KAAK,EAAE,KAAK,CAAC;YACb,KAAK,EAAE,wBAAwB,CAAC;YAChC,QAAQ,EAAE,cAAc,CAAC;YACzB,KAAK,EAAE,MAAM,eAAe,CAAC;YAC7B,GAAG,EAAE,MAAM,WAAW,CAAC;SACxB,KAAK,IAAI,GAAG,IAAI,CAAC;KACnB;CAIF;AA2HD;;;;GAIG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE;IAAE,MAAM,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,SAAS,EAAE,CAAA;CAAE,GAAG,MAAM,CAiHtF"}