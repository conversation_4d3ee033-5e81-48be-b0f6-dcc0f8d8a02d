{"hash": "1246bb5d", "configHash": "a762cc2d", "lockfileHash": "112ecf1e", "browserHash": "bba75e0f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "38f46bf9", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fbc710a6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1169c5b9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "bdbb3077", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "2c3494c4", "needsInterop": false}, "@tiptap/extension-text-align": {"src": "../../@tiptap/extension-text-align/dist/index.js", "file": "@tiptap_extension-text-align.js", "fileHash": "4a20001a", "needsInterop": false}, "@tiptap/react": {"src": "../../@tiptap/react/dist/index.js", "file": "@tiptap_react.js", "fileHash": "e539177c", "needsInterop": false}, "@tiptap/starter-kit": {"src": "../../@tiptap/starter-kit/dist/index.js", "file": "@tiptap_starter-kit.js", "fileHash": "b27660c6", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "91dc0b98", "needsInterop": false}, "dayjs": {"src": "../../dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "2f6334ed", "needsInterop": true}, "jwt-decode": {"src": "../../jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "3b6eb133", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "48f22a1e", "needsInterop": false}, "react-day-picker": {"src": "../../react-day-picker/dist/esm/index.js", "file": "react-day-picker.js", "fileHash": "c95ce572", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c65e948d", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "2df0afb5", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "5d0c2f3a", "needsInterop": false}, "yup": {"src": "../../yup/index.esm.js", "file": "yup.js", "fileHash": "c07824ad", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "41da0060", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "ee47db77", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "6313073d", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-OX56QJAV": {"file": "chunk-OX56QJAV.js"}, "chunk-RUPNRBO7": {"file": "chunk-RUPNRBO7.js"}, "chunk-76BQHZKB": {"file": "chunk-76BQHZKB.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}