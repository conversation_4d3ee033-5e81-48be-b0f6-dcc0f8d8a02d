import {
  SelectProps,
  VariantProps,
  SizeClassesProps,
} from "@/interfaces/components/input/selectProps";
import View from "./view";
import Text from "./text";

const sizeClasses: SizeClassesProps = {
  small: "h-9 text-sm px-3 py-2",
  medium: "h-10 text-base px-3 py-2.5",
  large: "h-12 text-lg px-4 py-3",
  default: "h-10 text-base px-3 py-2.5",
};

const variantClasses: VariantProps = {
  error: "border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-100 bg-red-50 dark:bg-red-900/20",
  default: "border border-border bg-background hover:border-primary-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
  outlined: "border border-border bg-transparent hover:border-primary-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
  filled: "border border-transparent bg-primary-10 dark:bg-primary-900/20 hover:bg-primary-20 dark:hover:bg-primary-900/30 focus:bg-background focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
};

const Select: React.FC<SelectProps> = ({
  id,
  name,
  label,
  style,
  value,
  error,
  onBlur,
  onChange,
  leftIcon,
  rightIcon,
  className,
  placeholder,
  options = [],
  defaultValue,
  disabled = false,
  fullWidth = false,
  variant = "default",
  selectSize = "medium",
  required = false,
  ...rest
}) => {
  return (
    <View>
      {label && (
        <label htmlFor={name}>
          {label}
          {required && <span className="text-red-600">*</span>}
        </label>
      )}

      <View className={`relative ${fullWidth ? "w-full" : ""}`}>
        {leftIcon && (
          <View className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-500">
            {leftIcon}
          </View>
        )}
        <select
          onChange={onChange}
          onBlur={onBlur}
          disabled={disabled}
          value={value}
          id={id}
          name={name}
          className={`
            w-full rounded-lg focus:outline-none transition-all duration-200 appearance-none
            placeholder:text-muted-foreground
            disabled:opacity-50 disabled:cursor-not-allowed
            ${variantClasses[variant]}
            ${sizeClasses[selectSize]}
            ${leftIcon ? "pl-10" : ""}
            ${rightIcon ? "pr-10" : "pr-10"}
            ${error ? variantClasses.error : ""}
            ${className || ""}
          `}
          style={style}
          {...rest}
        >
          {placeholder && (
            <option value="" disabled selected>
              {placeholder}
            </option>
          )}
          {options?.map((option) => (
            <option
              key={option?.value + ""}
              value={option?.value}
              selected={option?.value === value ? true : false}
              disabled={option?.disabled}
              className="dark:bg-card dark:border-b dark:border-background dark:shadow-md"
            >
              {option?.label}
            </option>
          ))}
        </select>
        {rightIcon ? (
          <View className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-500">
            {rightIcon}
          </View>
        ) : (
          <View className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-muted-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="transition-transform duration-200"
            >
              <path d="m6 9 6 6 6-6" />
            </svg>
          </View>
        )}
        {error && (
          <Text as="p" className="text-red-500 text-sm mt-1">
            {error}
          </Text>
        )}
      </View>
    </View>
  );
};

export default Select;
