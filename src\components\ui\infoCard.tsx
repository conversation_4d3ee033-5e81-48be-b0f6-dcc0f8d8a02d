import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = React.memo(({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`
      bg-card border border-border/50 dark:border-border/20
      rounded-xl p-6
      shadow-sm hover:shadow-md
      transition-all duration-300 ease-in-out
      hover:scale-[1.02] hover:-translate-y-1
      backdrop-blur-sm
      relative overflow-hidden
      group
      ${className}
    `}
    style={style}
  >
    <View className="flex items-center mb-3">
      <Text
        as="h3"
        className={`text-sm font-medium text-muted-foreground uppercase tracking-wide ${
          titleStyle ? titleStyle : ""
        }`}
      >
        {label}
      </Text>
    </View>
    <Text
      weight="font-bold"
      className={`text-2xl font-bold text-foreground mb-1 ${
        isLink ? "text-primary cursor-pointer hover:underline transition-colors" : ""
      } ${valueStyle ? valueStyle : ""}`}
    >
      {value}
    </Text>
    {subValue && (
      <Text
        className={`text-sm text-text-lighter ${
          subValueStyle ? subValueStyle : ""
        }`}
      >
        {subValue}
      </Text>
    )}
    {icon && (
      <View
        className={`
          p-3 rounded-xl bg-primary-50 dark:bg-primary-900/20 text-primary-500
          absolute top-6 right-6
          transition-all duration-300 group-hover:scale-110 group-hover:rotate-3
          ${iconStyle ? iconStyle : ""}
        `}
      >
        {icon}
      </View>
    )}

    {/* Subtle gradient overlay */}
    <View className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary-50/10 dark:to-primary-900/10 pointer-events-none" />
  </View>
));

InfoCard.displayName = 'InfoCard';

export default InfoCard;
