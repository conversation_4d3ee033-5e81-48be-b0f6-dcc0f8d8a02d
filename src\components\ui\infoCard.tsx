import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = React.memo(({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`
      bg-card/80 backdrop-blur-xl border border-border/30 dark:border-border/10
      rounded-2xl p-6
      shadow-lg shadow-black/5 dark:shadow-black/20
      hover:shadow-2xl hover:shadow-black/10 dark:hover:shadow-black/30
      transition-all duration-500 ease-out
      hover:scale-[1.02] hover:-translate-y-2
      relative overflow-hidden
      group
      ring-1 ring-white/10 dark:ring-white/5
      ${className}
    `}
    style={style}
  >
    {/* Premium background effects */}
    <View className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 dark:from-white/2 dark:to-black/10 pointer-events-none" />
    <View className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />

    <View className="relative z-10">
      <View className="flex items-center justify-between mb-4">
        <Text
          as="h3"
          className={`text-xs font-semibold text-muted-foreground/80 uppercase tracking-wider ${
            titleStyle ? titleStyle : ""
          }`}
        >
          {label}
        </Text>
        {icon && (
          <View
            className={`
              p-2.5 rounded-xl bg-gradient-to-br from-primary-50 to-primary-100/50
              dark:from-primary-900/20 dark:to-primary-800/10
              text-primary-600 dark:text-primary-400
              ring-1 ring-primary-200/50 dark:ring-primary-800/30
              transition-all duration-300 group-hover:scale-110 group-hover:rotate-3
              shadow-sm
              ${iconStyle ? iconStyle : ""}
            `}
          >
            {icon}
          </View>
        )}
      </View>

      <Text
        weight="font-bold"
        className={`text-3xl font-bold text-foreground mb-2 leading-none ${
          isLink ? "text-primary cursor-pointer hover:underline transition-colors" : ""
        } ${valueStyle ? valueStyle : ""}`}
      >
        {value}
      </Text>

      {subValue && (
        <Text
          className={`text-sm text-muted-foreground/70 font-medium ${
            subValueStyle ? subValueStyle : ""
          }`}
        >
          {subValue}
        </Text>
      )}
    </View>

    {/* Premium glow effect on hover */}
    <View className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary-500/0 to-secondary-500/0 group-hover:from-primary-500/5 group-hover:to-secondary-500/5 transition-all duration-500 pointer-events-none" />

    {/* Subtle corner accent */}
    <View className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-primary-500/10 to-transparent rounded-tl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
  </View>
));

InfoCard.displayName = 'InfoCard';

export default InfoCard;
