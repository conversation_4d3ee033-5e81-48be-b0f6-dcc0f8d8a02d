// Input component
import { InputProps } from "@/interfaces/components/input/input";
import View from "./view";

interface CSSProps {
  small: string;
  medium: string;
  large: string;
}
interface VariantProps {
  default: string;
  filled: string;
  error: string;
  outlined: string;
}

const sizeClasses: CSSProps = {
  small: "h-9 text-sm px-3 py-2",
  medium: "h-10 text-base px-3 py-2.5",
  large: "h-12 text-lg px-4 py-3",
};

const setVariantHandler: VariantProps = {
  outlined: "border border-border bg-transparent hover:border-primary-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-100",
  error: "border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-100 bg-red-50 dark:bg-red-900/20",
  default:
    "border border-border bg-background hover:border-primary-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-100 transition-all duration-200",
  filled:
    "border border-transparent bg-primary-10 dark:bg-primary-900/20 hover:bg-primary-20 dark:hover:bg-primary-900/30 focus:bg-background focus:border-primary-500 focus:ring-2 focus:ring-primary-100 transition-all duration-200",
};

const Input: React.FC<InputProps> = ({
  id,
  ref,
  name,
  style,
  error,
  value,
  label,
  onBlur,
  onChange,
  leftIcon,
  rightIcon,
  className,
  type = "text",
  disabled = false,
  fullWidth = false,
  variant = "default",
  inputSize = "medium",
  placeholder = "Enter text",
  required = false,
  ...rest
}) => {
  return (
    <View className="relative w-full">
      {label && (
        <label htmlFor={name}>
          {label}
          {required && <span className="text-red-600 ">*</span>}
        </label>
      )}
      <View className={`input-wrapper ${fullWidth ? "w-full" : ""}`}>
        {leftIcon && <View className="input-icon-left">{leftIcon}</View>}
        <input
          id={id}
          ref={ref}
          name={name}
          type={type}
          value={value?.toString()}
          style={style}
          onBlur={onBlur}
          autoComplete="off"
          onChange={onChange}
          disabled={disabled}
          placeholder={placeholder}
          className={`
            w-full rounded-lg focus:outline-none transition-all duration-200
            placeholder:text-muted-foreground
            disabled:opacity-50 disabled:cursor-not-allowed
            focus-visible:ring-2 focus-visible:ring-primary-200 focus-visible:ring-offset-1
            ${setVariantHandler[variant]}
            ${sizeClasses[inputSize]}
            ${error ? setVariantHandler.error : ""}
            ${className || ""}
          `}
          aria-invalid={!!error}
          aria-describedby={error ? `${id}-error` : undefined}
          {...rest}
        />
        {rightIcon && <div className="input-icon-right">{rightIcon}</div>}
      </View>
      {error && (
          <p
            id={`${id}-error`}
            className="text-red-500 text-sm mt-1"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
    </View>
  );
};

export default Input;
