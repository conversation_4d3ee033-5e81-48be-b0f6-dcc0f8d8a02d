{"version": 3, "file": "Extension.d.ts", "sourceRoot": "", "sources": ["../src/Extension.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAEtD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAEpC,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAC1C,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAA;AAChC,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAA;AAChC,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAC1C,OAAO,EAEL,UAAU,EACV,gBAAgB,EAChB,uBAAuB,EACvB,YAAY,EACZ,WAAW,EACZ,MAAM,YAAY,CAAA;AAInB,OAAO,QAAQ,cAAc,CAAC;IAC5B,UAAU,eAAe,CAAC,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG;QAEpD,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;QAElB;;;;;WAKG;QACH,IAAI,EAAE,MAAM,CAAA;QAEZ;;;;;WAKG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAA;QAEjB;;;;;;;WAOG;QACH,cAAc,CAAC,EAAE,OAAO,CAAA;QAExB;;;;;;;;;WASG;QACH,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE;YAClB,IAAI,EAAE,MAAM,CAAA;YACZ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,CAAA;SAC1F,KAAK,OAAO,CAAA;QAEb;;;;;;;;WAQG;QACH,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE;YAClB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,CAAA;SAC1F,KAAK,OAAO,CAAA;QAEb;;;;;;;;;;;;;;;;;;;;;;;;;WAyBG;QACH,mBAAmB,CAAC,EAAE,CAAC,IAAI,EAAE;YAC3B,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,UAAU,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAA;YAC3B,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAA;SAC/E,KAAK,gBAAgB,CAAA;QAEtB;;;;;;;;;WASG;QACH,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE;YACnB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;SACvE,KAAK,OAAO,CAAC,WAAW,CAAC,CAAA;QAE1B;;;;;;;;;WASG;QACH,oBAAoB,CAAC,EAAE,CAAC,IAAI,EAAE;YAC5B,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAA;SAChF,KAAK;YACJ,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,CAAA;SACvC,CAAA;QAED;;;;;;;;;;;;WAYG;QACH,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE;YACrB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAA;SACzE,KAAK,SAAS,EAAE,CAAA;QAEjB;;;;;;;;;;;;WAYG;QACH,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE;YACrB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAA;SACzE,KAAK,SAAS,EAAE,CAAA;QAEjB;;;;;;;;;WASG;QACH,qBAAqB,CAAC,EAAE,CAAC,IAAI,EAAE;YAC7B,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAA;SACjF,KAAK,MAAM,EAAE,CAAA;QAEd;;;;;;;;;;;WAWG;QACH,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE;YACrB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAA;SACzE,KAAK,UAAU,CAAA;QAEhB;;;;;;;;;WASG;QACH,gBAAgB,CAAC,EACb,CAAC,CACC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAA;SAC5E,EACD,SAAS,EAAE,IAAI,KACZ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GACzB,IAAI,CAAA;QAER;;;;;;;;;WASG;QACH,gBAAgB,CAAC,EACb,CAAC,CACC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAA;SAC5E,EACD,SAAS,EAAE,IAAI,KACZ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GACzB,IAAI,CAAA;QAER;;WAEG;QACH,cAAc,CAAC,EACX,CAAC,CAAC,IAAI,EAAE;YACN,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;SAC1E,KAAK,IAAI,CAAC,GACX,IAAI,CAAA;QAER;;WAEG;QACH,QAAQ,CAAC,EACL,CAAC,CAAC,IAAI,EAAE;YACN,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;SACpE,KAAK,IAAI,CAAC,GACX,IAAI,CAAA;QAER;;WAEG;QACH,QAAQ,CAAC,EACL,CAAC,CAAC,IAAI,EAAE;YACN,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;SACpE,KAAK,IAAI,CAAC,GACX,IAAI,CAAA;QAER;;WAEG;QACH,iBAAiB,CAAC,EACd,CAAC,CAAC,IAAI,EAAE;YACN,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAA;SAC7E,KAAK,IAAI,CAAC,GACX,IAAI,CAAA;QAER;;WAEG;QACH,aAAa,CAAC,EACV,CAAC,CACC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAA;SACzE,EACD,KAAK,EAAE;YACL,MAAM,EAAE,MAAM,CAAA;YACd,WAAW,EAAE,WAAW,CAAA;SACzB,KACE,IAAI,CAAC,GACV,IAAI,CAAA;QAER;;WAEG;QACH,OAAO,CAAC,EACJ,CAAC,CACC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;SACnE,EACD,KAAK,EAAE;YACL,KAAK,EAAE,UAAU,CAAA;SAClB,KACE,IAAI,CAAC,GACV,IAAI,CAAA;QAER;;WAEG;QACH,MAAM,CAAC,EACH,CAAC,CACC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;SAClE,EACD,KAAK,EAAE;YACL,KAAK,EAAE,UAAU,CAAA;SAClB,KACE,IAAI,CAAC,GACV,IAAI,CAAA;QAER;;WAEG;QACH,SAAS,CAAC,EACN,CAAC,CAAC,IAAI,EAAE;YACN,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,MAAM,EAAE,YAAY,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;SACrE,KAAK,IAAI,CAAC,GACX,IAAI,CAAA;KACT;CACF;AAED;;;GAGG;AACH,qBAAa,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG;IACjD,IAAI,SAAc;IAElB,IAAI,SAAc;IAElB,MAAM,EAAE,SAAS,GAAG,IAAI,CAAO;IAE/B,KAAK,EAAE,SAAS,GAAG,IAAI,CAAO;IAE9B,OAAO,EAAE,OAAO,CAAA;IAEhB,OAAO,EAAE,OAAO,CAAA;IAEhB,MAAM,EAAE,eAAe,CAGtB;gBAEW,MAAM,GAAE,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAM;IAiCnE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,MAAM,GAAE,OAAO,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAM;IAI3E,SAAS,CAAC,OAAO,GAAE,OAAO,CAAC,OAAO,CAAM;IAkBxC,MAAM,CAAC,eAAe,GAAG,OAAO,EAAE,eAAe,GAAG,OAAO,EACzD,cAAc,GAAE,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,CAAM;CA+BlF"}