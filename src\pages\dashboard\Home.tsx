import { useEffect, useState } from "react";
import DashboardLayout from "@/components/DashboardLayout";
import Button from "@/components/button";
// import { Card } from "@/components/ui/card";
import {
  UserPlus,
  CalendarCheck,
  Bed,
  Stethoscope,
  Users,
  // ArrowUp,
  // ArrowDown,
  // TrendingUp,
} from "lucide-react";
import { useSelector } from "react-redux";
// import { logoutSlice } from "@/actions/slices/auth";

import { useDashboard } from "@/actions/calls/dashboard";
import View from "@/components/view";
import Text from "@/components/text";
import InfoCard from "@/components/ui/infoCard";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import { useSearchParams } from "react-router-dom";
import PaginationComponent from "@/components/Pagination";
import Input from "@/components/input";
import Select from "@/components/Select";
import DataSort, { SortOption } from "@/components/SortData";
import { handleSortChange } from "@/utils/helperFunctions";
import Filter from "../filter";
import SearchBar from "@/components/ui/search-bar";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import dayjs from "dayjs";
import {
  CONSULTATION_DETAILS_URL,
  CONSULTATION_TABLE_URL,
  DATE_FORMAT,
} from "@/utils/urls/frontend";
import { Link } from "react-router-dom";
import BouncingLoader from "@/components/BouncingLoader";




// const StatCard = ({
//   title,
//   value,
//   icon,
//   trend,
//   trendValue,
// }: {
//   title: string;
//   value: string;
//   icon?: React.ReactNode;
//   trend?: "up" | "down" | "neutral";
//   trendValue?: string;
// }) => {

//   useEffect(() => {
//     // dispatch(logoutSlice());
//   }, []);

//   return (
//     <Card className="p-6 bg-card hover:shadow-lg transition-shadow duration-200">
//       <View className="flex justify-between items-start">
//         <View>
//           <Text as="p" className="text-text-light text-sm font-medium mb-1">{title}</Text>
//           <Text as="h3" className="text-2xl font-bold text-text-DEFAULT">{value}</Text>
//         </View>
//         <View className="p-3 rounded-full bg-primary-50 text-primary-500">
//           {icon}
//         </View>
//       </View>
//       {
//         trend && trendValue && (
//           <View className="mt-4 flex items-center">
//         <Text as="span"
//           className={`text-sm font-medium flex items-center ${
//             trend === "up"
//               ? "text-accent"
//               : trend === "down"
//               ? "text-danger"
//               : "text-text-light"
//           }`}
//         >
//           {trend === "up" ? (
//             <ArrowUp size={16} className="mr-1" />
//           ) : trend === "down" ? (
//             <ArrowDown size={16} className="mr-1" />
//           ) : (
//             <TrendingUp size={16} className="mr-1" />
//           )}
//           {trendValue}
//         </Text>
//         <Text as="span" className="text-sm text-text-lighter ml-1">vs last month</Text>
//       </View>
//         )
//       }
//     </Card>
//   );
// };

// const RecentAppointmentsCard = () => {
//   const appointments = [
//     {
//       id: 1,
//       patient: "John Doe",
//       doctor: "Dr. Sarah Smith",
//       time: "09:00 AM",
//       status: "Completed",
//     },
//     {
//       id: 2,
//       patient: "Jane Smith",
//       doctor: "Dr. Robert Johnson",
//       time: "10:30 AM",
//       status: "Scheduled",
//     },
//     {
//       id: 3,
//       patient: "Michael Brown",
//       doctor: "Dr. Lisa Anderson",
//       time: "11:45 AM",
//       status: "Cancelled",
//     },
//     {
//       id: 4,
//       patient: "Emily Davis",
//       doctor: "Dr. James Wilson",
//       time: "02:15 PM",
//       status: "Scheduled",
//     },
//   ];

//   return (
//     <Card className="p-6">
//       <div className="flex justify-between items-center mb-6">
//         <h3 className="text-lg font-bold text-text-DEFAULT">
//           Recent Appointments
//         </h3>
//         <a
//           href="#"
//           className="text-sm text-primary hover:text-primary-600 font-medium"
//         >
//           View all
//         </a>
//       </div>
//       <div className="overflow-x-auto">
//         <table className="w-full">
//           <thead>
//             <tr className="border-b border-neutral-200">
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Patient
//               </th>
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Doctor
//               </th>
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Time
//               </th>
//               <th className="py-3 px-4 text-left text-sm font-medium text-text-light">
//                 Status
//               </th>
//             </tr>
//           </thead>
//           <tbody>
//             {appointments.map((appointment) => (
//               <tr
//                 key={appointment.id}
//                 className="border-b border-neutral-200 hover:bg-neutral-50"
//               >
//                 <td className="py-3 px-4 text-sm">{appointment.patient}</td>
//                 <td className="py-3 px-4 text-sm">{appointment.doctor}</td>
//                 <td className="py-3 px-4 text-sm">{appointment.time}</td>
//                 <td className="py-3 px-4 text-sm">
//                   <span
//                     className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
//                       appointment.status === "Completed"
//                         ? "bg-accent-50 text-accent"
//                         : appointment.status === "Scheduled"
//                         ? "bg-primary-50 text-primary"
//                         : "bg-neutral-100 text-text-light"
//                     }`}
//                   >
//                     {appointment.status}
//                   </span>
//                 </td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>
//     </Card>
//   );
// };

const Dashboard = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { getDashboardDataHandler , cleanUp} = useDashboard();
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );

  const [isLoading,setIsLoading] = useState(true);

  

  // console.log(checkLoadinStatus.isLoading, "isLoading");
  
 
  // const consultationListData = useSelector(
  //   (state: any) => state.consultation.consultationListData
  // );
  const dashboardData = useSelector(
    (state: any) => state?.dashboard?.dashboardData
  );

  // useEffect(() => {
  //   getDashboardDataHandler(() => {});
  // }, []);

  useEffect(() => {
    getDashboardDataHandler(
      searchParams?.get("currentPage") ?? 1,
      () => {},
      searchParams.get("search") ?? null,
      searchParams.get("sort_by") ?? null,
      searchParams.get("sort_order") ?? null,
      filterData,
      (status) => {
        setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
      }
    );
    return () => {
      cleanUp();
    };
  }, [
    filterData,
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const sortOptions: SortOption[] = [
    { label: "Patient ID (A-Z)", value: "patient_id", order: "asc" },
    { label: "Patient ID (Z-A)", value: "patient_id", order: "desc" },
    { label: "Patient Name (A-Z)", value: "patient_name", order: "asc" },
    { label: "Patient Name (Z-A)", value: "patient_name", order: "desc" },
    { label: "Next Visit Date (A-Z)", value: "next_visit_date", order: "asc" },
    { label: "Next Visit Date (Z-A)", value: "next_visit_date", order: "desc" },
    { label: "Status (A-Z)", value: "status", order: "asc" },
    { label: "Status (Z-A)", value: "status", order: "desc" },
    { label: "Payment Status (A-Z)", value: "payment_status", order: "asc" },
    { label: "Payment Status (Z-A)", value: "payment_status", order: "desc" },
  ];
  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  return (
    <DashboardLayout>
      <View className="space-y-8">
        <View className="fixed top-4 left-0  w-full z-50">
          <BouncingLoader  isLoading={isLoading} />
        </View>

        {/* Premium Header Section */}
      <View className="mb-12">
        <View className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <View className="space-y-2">
            <Text
              as="h1"
              className="text-4xl font-bold bg-gradient-to-r from-foreground via-primary-600 to-secondary-600 bg-clip-text text-transparent"
            >
              Dashboard
            </Text>
            <Text className="text-lg text-muted-foreground font-medium">
              Welcome back to {import.meta.env.VITE_HOSPITAL_NAME} Hospital Management System
            </Text>
            <Text className="text-sm text-muted-foreground/80">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          </View>

          {/* Quick Actions */}
          <View className="flex items-center gap-3">
            <Button
              size="small"
              variant="outline"
              className="bg-card/50 backdrop-blur-sm border-border/50 hover:bg-primary-50 hover:border-primary-200 transition-all duration-300"
            >
              <UserPlus size={16} className="mr-2" />
              Add Patient
            </Button>
            <Button
              size="small"
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 shadow-lg shadow-primary-500/25 transition-all duration-300"
            >
              <CalendarCheck size={16} className="mr-2" />
              New Appointment
            </Button>
          </View>
        </View>
      </View>

      {/* Premium Welcome Banner */}
      <View className="mb-12">
        <View className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-primary-500 via-primary-600 to-secondary-600 p-8 lg:p-12 shadow-2xl shadow-primary-500/20">
          {/* Background Pattern */}
          <View className="absolute inset-0 opacity-30">
            <View className="w-full h-full bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]"></View>
          </View>

          <View className="relative z-10">
            <Text as="h2" className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Welcome to Hospital Management
            </Text>
            <Text className="text-xl text-white/90 mb-6 max-w-2xl">
              Monitor your hospital operations and patient care metrics in real-time with our comprehensive dashboard
            </Text>

            {/* Status Indicator */}
            <View className="flex items-center gap-3">
              <View className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <View className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></View>
                <Text className="text-white/90 text-sm font-medium">All Systems Operational</Text>
              </View>
              <View className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Text className="text-white/90 text-sm font-medium">Last updated: {new Date().toLocaleTimeString()}</Text>
              </View>
            </View>
          </View>

          {/* Decorative Elements */}
          <View className="absolute top-4 right-4 w-32 h-32 bg-white/5 rounded-full blur-3xl"></View>
          <View className="absolute bottom-4 left-4 w-24 h-24 bg-secondary-400/20 rounded-full blur-2xl"></View>
        </View>
      </View>

      {/* Premium Stats Grid */}
      <View className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        {/* Total Patients Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-50 to-emerald-100/50 dark:from-emerald-950/20 dark:to-emerald-900/10 border border-emerald-200/50 dark:border-emerald-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-emerald-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-emerald-500/10 dark:bg-emerald-400/10 ring-1 ring-emerald-500/20">
              <UserPlus size={24} className="text-emerald-600 dark:text-emerald-400" />
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-emerald-600/70 dark:text-emerald-400/70 uppercase tracking-wider">Total Patients</Text>
              <Text className="text-3xl font-bold text-emerald-700 dark:text-emerald-300 mt-1">
                {dashboardData?.totalPatients ?? "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-emerald-600/80 dark:text-emerald-400/80">Active registrations</Text>
            <View className="flex items-center gap-1 text-emerald-600 dark:text-emerald-400">
              <View className="w-1.5 h-1.5 bg-emerald-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Live</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-emerald-500/5 rounded-full blur-xl"></View>
        </View>

        {/* Appointments Today Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/20 dark:to-blue-900/10 border border-blue-200/50 dark:border-blue-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-blue-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-blue-500/10 dark:bg-blue-400/10 ring-1 ring-blue-500/20">
              <CalendarCheck size={24} className="text-blue-600 dark:text-blue-400" />
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-blue-600/70 dark:text-blue-400/70 uppercase tracking-wider">Appointments Today</Text>
              <Text className="text-3xl font-bold text-blue-700 dark:text-blue-300 mt-1">
                {dashboardData?.totalAppointments ?? "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-blue-600/80 dark:text-blue-400/80">Scheduled visits</Text>
            <View className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
              <View className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Today</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-blue-500/5 rounded-full blur-xl"></View>
        </View>

        {/* OPD Cases Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-950/20 dark:to-purple-900/10 border border-purple-200/50 dark:border-purple-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-purple-500/10 dark:bg-purple-400/10 ring-1 ring-purple-500/20">
              <Stethoscope size={24} className="text-purple-600 dark:text-purple-400" />
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-purple-600/70 dark:text-purple-400/70 uppercase tracking-wider">OPD Cases</Text>
              <Text className="text-3xl font-bold text-purple-700 dark:text-purple-300 mt-1">
                {dashboardData?.totalOPD ?? "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-purple-600/80 dark:text-purple-400/80">Outpatient visits</Text>
            <View className="flex items-center gap-1 text-purple-600 dark:text-purple-400">
              <View className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Active</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-purple-500/5 rounded-full blur-xl"></View>
        </View>

        {/* IPD Cases Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-950/20 dark:to-orange-900/10 border border-orange-200/50 dark:border-orange-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-orange-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-orange-500/10 dark:bg-orange-400/10 ring-1 ring-orange-500/20">
              <Bed size={24} className="text-orange-600 dark:text-orange-400" />
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-orange-600/70 dark:text-orange-400/70 uppercase tracking-wider">IPD Cases</Text>
              <Text className="text-3xl font-bold text-orange-700 dark:text-orange-300 mt-1">
                {dashboardData?.totalIPD ?? "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-orange-600/80 dark:text-orange-400/80">Inpatient admissions</Text>
            <View className="flex items-center gap-1 text-orange-600 dark:text-orange-400">
              <View className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Current</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-orange-500/5 rounded-full blur-xl"></View>
        </View>

        {/* <InfoCard
          label="Total Consultations"
          value={dashboardData?.totalAppointments ?? "N/A"}
          valueStyle="!text-[#ED8936] !text-3xl"
          icon={<MessageSquareText size={24} />}
          className="dark:bg-card"
          iconStyle="!text-[#ED8936] !bg-[#FFF5E5]"
        /> */}

        {/* <InfoCard
          label="Total Active Patients"
          value={dashboardData?.totalPatientsActive}
          valueStyle="text-success !text-3xl"
          icon={<UserCheck size={24} />}
          className="dark:bg-card"
        />
        */}
        {/* Moved to second row if needed */}

        {/* <StatCard
          title="Total Active Patients"
          value={dashboardData?.totalPatientsActive}
          icon={<UserPlus size={24} />}
          // trend="up"
          // trendValue="12.5%"
        />
        <StatCard
          title="Total Users"
          value={dashboardData?.totalUsers}
          icon={<Users size={24} />}
          // trend="neutral"
          // trendValue="0.8%"
        /> */}

        {/* <button onClick={() => {
        toast({
          title: "Success!",
          description: "Your action was completed successfully",
          variant:"success"
        });
      }}>test</button> */}
        {/* <StatCard 
          title="Appointments" 
          value="128" 
          icon={<Calendar size={24} />}
          trend="up"
          trendValue="5.2%"
        />
        <StatCard 
          title="Hospital Capacity" 
          value="72%" 
          icon={<Activity size={24} />}
          trend="down"
          trendValue="3.1%"
        /> */}
      </View>

      {/* Secondary Stats & Quick Info */}
      <View className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {/* Total Users Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-50 to-indigo-100/50 dark:from-indigo-950/20 dark:to-indigo-900/10 border border-indigo-200/50 dark:border-indigo-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-indigo-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-indigo-500/10 dark:bg-indigo-400/10 ring-1 ring-indigo-500/20">
              <Users size={24} className="text-indigo-600 dark:text-indigo-400" />
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-indigo-600/70 dark:text-indigo-400/70 uppercase tracking-wider">Total Users</Text>
              <Text className="text-2xl font-bold text-indigo-700 dark:text-indigo-300 mt-1">
                {dashboardData?.totalUsers ?? "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-indigo-600/80 dark:text-indigo-400/80">System users</Text>
            <View className="flex items-center gap-1 text-indigo-600 dark:text-indigo-400">
              <View className="w-1.5 h-1.5 bg-indigo-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Active</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-indigo-500/5 rounded-full blur-xl"></View>
        </View>

        {/* Beds Occupied Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-rose-50 to-rose-100/50 dark:from-rose-950/20 dark:to-rose-900/10 border border-rose-200/50 dark:border-rose-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-rose-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-rose-500/10 dark:bg-rose-400/10 ring-1 ring-rose-500/20">
              <Bed size={24} className="text-rose-600 dark:text-rose-400" />
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-rose-600/70 dark:text-rose-400/70 uppercase tracking-wider">Beds Occupied</Text>
              <Text className="text-2xl font-bold text-rose-700 dark:text-rose-300 mt-1">
                {dashboardData?.noOfBedsOccupied ?? "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-rose-600/80 dark:text-rose-400/80">Current occupancy</Text>
            <View className="flex items-center gap-1 text-rose-600 dark:text-rose-400">
              <View className="w-1.5 h-1.5 bg-rose-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Live</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-rose-500/5 rounded-full blur-xl"></View>
        </View>

        {/* System Status Card */}
        <View className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-950/20 dark:to-green-900/10 border border-green-200/50 dark:border-green-800/30 p-6 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-green-500/20">
          <View className="flex items-start justify-between mb-4">
            <View className="p-3 rounded-xl bg-green-500/10 dark:bg-green-400/10 ring-1 ring-green-500/20">
              <View className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <View className="w-2 h-2 bg-white rounded-full"></View>
              </View>
            </View>
            <View className="text-right">
              <Text className="text-xs font-medium text-green-600/70 dark:text-green-400/70 uppercase tracking-wider">System Status</Text>
              <Text className="text-lg font-bold text-green-700 dark:text-green-300 mt-1">
                All Systems Operational
              </Text>
            </View>
          </View>
          <View className="flex items-center justify-between">
            <Text className="text-sm text-green-600/80 dark:text-green-400/80">Everything running smoothly</Text>
            <View className="flex items-center gap-1 text-green-600 dark:text-green-400">
              <View className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></View>
              <Text className="text-xs font-medium">Online</Text>
            </View>
          </View>
          <View className="absolute -bottom-2 -right-2 w-16 h-16 bg-green-500/5 rounded-full blur-xl"></View>
        </View>
      </View>

      {/* Premium Consultations Section */}
      <View className="mb-8">
        <View className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
          <View className="space-y-2">
            <Text as="h2" className="text-3xl font-bold bg-gradient-to-r from-foreground to-primary-600 bg-clip-text text-transparent">
              Upcoming Consultations
            </Text>
            <Text className="text-lg text-muted-foreground">
              Recent patient consultations and appointments
            </Text>
          </View>
          <Button
            variant="outline"
            size="small"
            className="bg-card/50 backdrop-blur-sm border-border/50 hover:bg-primary-50 hover:border-primary-200 transition-all duration-300"
          >
            View All Consultations
          </Button>
        </View>
      </View>

      <Card className="w-full mb-8 overflow-hidden border-0 shadow-2xl bg-card/80 backdrop-blur-xl rounded-3xl">
        <DynamicTable
          tableHeaders={[
            "Patient Number",
            "Patient Name",
            "Next Visit Date",
            "Status",
            "Payment Status",
            // "Actions",
          ]}
          tableData={dashboardData?.consultation?.data?.map((data: any) => [
            <Link
              to={
                CONSULTATION_TABLE_URL +
                CONSULTATION_DETAILS_URL +
                "/" +
                data.id
              }
              className="font-medium text-text-DEFAULT hover:text-primary hover:underline"
            >
              {data?.patient_number}
            </Link>,
            data?.patient_name,
            data?.next_visit_date
              ? dayjs(data?.next_visit_date).format(DATE_FORMAT)
              : "NA",
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.status)}
            >
              {data?.status || "N/A"}
            </Text>,
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.payment_status)}
            >
              {data?.payment_status || "N/A"}
            </Text>,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                      search: value,
                    },
                    { replace: true }
                  );
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            filter: (
              <Filter
                title="Consultation Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                    },
                    { replace: true }
                  );
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_number" placeholder="Patient Number" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="patient_name" placeholder="Patient Name" />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input name="referred_by_name" placeholder="Referred By" />
                  // </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     name="appointment_number"
                  //     placeholder="Appointment Number"
                  //   />
                  // </View>,
                  // <View className="w-full my-4">
                  //   <Input name="doctor_name" placeholder="Doctor Name" />
                  // </View>,
                  <View className="w-full my-4">
                    <Select
                      name="status"
                      placeholder="Select Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Select
                      name="payment_status"
                      placeholder="Select Payment Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     type="date"
                  //     name="next_visit_date"
                  //     placeholder="Next Visit Date"
                  //     onFocus={(e) => (e.target.type = "date")}
                  //   />
                  // </View>,
                ]}
              />
            ),
          }}
          footer={{
            pagination: (
             <PaginationComponent
                current_page={dashboardData?.consultation?.current_page}
                last_page={dashboardData?.consultation?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
      </View>
    </DashboardLayout>
  );
};

export default Dashboard;
