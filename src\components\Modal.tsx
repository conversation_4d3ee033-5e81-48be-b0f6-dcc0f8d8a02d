import View from "./view";
import Text from "./text";
import Button from "./button";
import React, { useEffect } from "react";

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  showCloseButton?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "full";
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  showCloseButton = true,
  size = "md",
}) => {
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    if (isOpen) {
      document.addEventListener("keydown", handleEsc);
    }
    return () => {
      document.removeEventListener("keydown", handleEsc);
    };
  }, [isOpen, onClose]);

  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full mx-4",
  };

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <View
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? "modal-title" : undefined}
      aria-describedby={description ? "modal-description" : undefined}
      onClick={handleBackdropClick}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm px-4 animate-in fade-in-0 duration-300"
    >
      <View
        className={`
          w-full ${sizeClasses[size]}
          bg-card border border-border/50 dark:border-border/20
          rounded-2xl shadow-2xl overflow-hidden
          backdrop-blur-md
          animate-in zoom-in-95 slide-in-from-bottom-4 duration-300
        `}
      >
        <View className="p-6 border-b border-border/20 flex justify-between items-start">
          <View>
            <Text
              as="h2"
              id="modal-title"
              className="text-xl font-semibold text-foreground"
            >
              {title}
            </Text>
            {description && (
              <p
                id="modal-description"
                className="text-muted-foreground mt-2 text-sm leading-relaxed"
              >
                {description}
              </p>
            )}
          </View>
          {showCloseButton && (
            <Button
              variant="ghost"
              onClick={onClose}
              className="h-8 w-8 rounded-lg flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-primary-50 dark:hover:bg-primary-900/20"
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </Button>
          )}
        </View>

        <View className="p-6 overflow-y-auto" style={{ maxHeight: "70vh" }}>
          {children}
        </View>

        {footer && (
          <View className="bg-primary-10/50 dark:bg-primary-900/10 p-6 border-t border-border/20">
            {footer}
          </View>
        )}
      </View>
    </View>
  );
};

export default Modal;
