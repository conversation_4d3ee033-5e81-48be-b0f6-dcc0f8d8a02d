import {
  Btn<PERSON><PERSON><PERSON>,
  Button<PERSON><PERSON>,
  SetButtonSizeProps,
} from "@/interfaces/components/button";

const setVariantCssHandler: Partial<Record<BtnVariant | "default", string>> = {
  default: "bg-primary hover:bg-primary-600 text-primary-foreground shadow-sm hover:shadow-md transition-all duration-200",
  ghost: "bg-transparent hover:bg-primary-10 dark:hover:bg-primary-900/20 text-primary hover:text-primary-600 transition-all duration-200",
  danger: "bg-red-600 hover:bg-red-700 text-white shadow-sm hover:shadow-md transition-all duration-200",
  primary: "bg-primary hover:bg-primary-600 text-primary-foreground shadow-sm hover:shadow-md transition-all duration-200 focus:ring-2 focus:ring-primary-200 focus:ring-offset-2",
  secondary: "bg-secondary hover:bg-secondary-600 text-secondary-foreground shadow-sm hover:shadow-md transition-all duration-200",
  outline: "border border-border bg-transparent hover:bg-primary-10 dark:hover:bg-primary-900/20 text-foreground hover:text-primary transition-all duration-200",
  text: "bg-transparent hover:bg-primary-10 dark:hover:bg-primary-900/20 text-primary hover:text-primary-600 transition-all duration-200",
};

const setButtonSize: SetButtonSizeProps = {
  large: "px-6 py-3 text-lg font-medium min-h-[48px]",
  small: "px-3 py-1.5 text-sm font-medium min-h-[32px]",
  medium: "px-4 py-2.5 text-base font-medium min-h-[40px]",
};
const Button: React.FC<ButtonProps> = ({
  style,
  onPress,
  className,
  size = "medium",
  loading = false,
  disabled = false,
  children = "Button",
  htmlType = "button",
  variant = "primary",
  ...props
}) => {
  return (
    <button
      type={htmlType}
      onClick={onPress}
      style={{ cursor: loading ? "progress" : "pointer", ...style }}
      disabled={disabled || loading}
      className={`
        ${loading ? "cursor-progress" : "cursor-pointer"}
        ${setButtonSize[size]}
        ${setVariantCssHandler[variant]}
        inline-flex items-center justify-center
        rounded-lg
        focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-200 focus-visible:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        active:scale-[0.98]
        ${className || ""}
      `}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {children}
    </button>
  );
};
export default Button;
