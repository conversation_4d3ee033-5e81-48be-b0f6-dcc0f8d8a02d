import {
  BtnVariant,
  ButtonProps,
  SetButtonSizeProps,
} from "@/interfaces/components/button";

const setVariantCssHandler: Partial<Record<BtnVariant | "default", string>> = {
  default: "bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-primary-foreground shadow-lg shadow-primary-500/25 hover:shadow-xl hover:shadow-primary-500/30 transition-all duration-300 ring-1 ring-primary-500/20",
  ghost: "bg-transparent hover:bg-primary-50/80 dark:hover:bg-primary-900/20 text-primary hover:text-primary-600 transition-all duration-300 backdrop-blur-sm",
  danger: "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/30 transition-all duration-300 ring-1 ring-red-500/20",
  primary: "bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-primary-foreground shadow-lg shadow-primary-500/25 hover:shadow-xl hover:shadow-primary-500/30 transition-all duration-300 focus:ring-2 focus:ring-primary-300 focus:ring-offset-2 ring-1 ring-primary-500/20",
  secondary: "bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-secondary-foreground shadow-lg shadow-secondary-500/25 hover:shadow-xl hover:shadow-secondary-500/30 transition-all duration-300 ring-1 ring-secondary-500/20",
  outline: "border border-border/50 bg-card/50 backdrop-blur-sm hover:bg-primary-50/80 dark:hover:bg-primary-900/20 text-foreground hover:text-primary hover:border-primary-300 transition-all duration-300 shadow-sm hover:shadow-md",
  text: "bg-transparent hover:bg-primary-50/80 dark:hover:bg-primary-900/20 text-primary hover:text-primary-600 transition-all duration-300 backdrop-blur-sm",
};

const setButtonSize: SetButtonSizeProps = {
  large: "px-8 py-4 text-lg font-semibold min-h-[52px] rounded-xl",
  small: "px-4 py-2 text-sm font-medium min-h-[36px] rounded-lg",
  medium: "px-6 py-3 text-base font-medium min-h-[44px] rounded-xl",
};
const Button: React.FC<ButtonProps> = ({
  style,
  onPress,
  className,
  size = "medium",
  loading = false,
  disabled = false,
  children = "Button",
  htmlType = "button",
  variant = "primary",
  ...props
}) => {
  return (
    <button
      type={htmlType}
      onClick={onPress}
      style={{ cursor: loading ? "progress" : "pointer", ...style }}
      disabled={disabled || loading}
      className={`
        ${loading ? "cursor-progress" : "cursor-pointer"}
        ${setButtonSize[size]}
        ${setVariantCssHandler[variant]}
        inline-flex items-center justify-center
        font-medium
        focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-300 focus-visible:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none
        active:scale-[0.97] hover:scale-[1.02]
        transform transition-all duration-300 ease-out
        relative overflow-hidden
        ${className || ""}
      `}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {children}
    </button>
  );
};
export default Button;
