import React from "react";
import { PanelRightClose } from "lucide-react";
import But<PERSON> from "@/components/button";
import { Link } from "react-router-dom";
import { USER_PROFILE_URL } from "@/utils/urls/backend";
import { useSelector } from "react-redux";
import View from "./view";
import ImageComponent from "./ui/ImageComponent";
import Text from "./text";
// import { useSelector } from "react-redux";

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean
}




const Header: React.FC<HeaderProps> = ({ toggleSidebar,sidebarOpen }) => {
  const settingsData = useSelector((state: any) => state.systemSettings.settings);
  
  return (
    <header className="h-16 border-b border-border/20 bg-card/80 backdrop-blur-md text-card-foreground shadow-sm transition-all duration-200 sticky top-0 z-10">
      <View className="flex h-full items-center justify-between px-6">
        {!sidebarOpen && (
          <Button
            onClick={toggleSidebar}
            className="text-muted-foreground hover:text-foreground hover:bg-primary-10 dark:hover:bg-primary-900/20 rounded-xl transition-all duration-200"
            variant="ghost"
          >
            <PanelRightClose size={20} />
          </Button>
        )}

        {/* Search */}
        {/* <div className="hidden md:block relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-text-lighter" />
          </div>
          <input
            type="search"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 w-full bg-neutral-100 border-none rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none"
          />
        </div> */}

        <View className="hidden md:block w-64">
          {/* <SearchBar /> */}
        </View>

        {/* User Menu & Notifications */}
        <View className="flex items-center gap-3">
          {/* Theme Toggle */}
          {/* <ThemeToggle /> */}

          {/* <Button
            className="relative p-1.5 text-neutral-500 hover:bg-neutral-100 rounded-lg"
            variant="ghost"
            // size="icon"
          >
            <Bell size={20} />
            <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-primary-500"></span>
          </Button> */}
          <Link to={USER_PROFILE_URL} className="group">
            <Button
              className="flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-primary-10 dark:hover:bg-primary-900/20 transition-all duration-200"
              variant="ghost"
            >
              {settingsData?.profile_image ? (
                <View className="h-9 w-9 rounded-full overflow-hidden ring-2 ring-primary-100 group-hover:ring-primary-200 transition-all duration-200">
                  <ImageComponent
                    src={import.meta.env.VITE_APP_URL + settingsData?.profile_image}
                    alt="User profile image"
                    className="rounded-full object-cover h-full w-full"
                  />
                </View>
              ) : (
                <View className="h-9 w-9 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center ring-2 ring-primary-100 group-hover:ring-primary-200 transition-all duration-200">
                  <Text as="span" className="text-sm font-semibold text-primary-600">
                    U
                  </Text>
                </View>
              )}

              {/* Optional: Add user name or status indicator */}
              <View className="hidden md:flex flex-col items-start">
                <Text as="span" className="text-sm font-medium text-foreground">
                  Profile
                </Text>
                <Text as="span" className="text-xs text-muted-foreground">
                  View settings
                </Text>
              </View>
            </Button>
          </Link>
        </View>
      </View>
    </header>
  );
};

export default Header;