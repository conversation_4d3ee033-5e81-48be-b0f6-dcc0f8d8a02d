import React from 'react';
import Button from '@/components/button';
import { <PERSON>pt<PERSON>, Moon, Sun } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { Theme } from '@/interfaces/systemSettings';
import { useSystemSettings } from '@/actions/calls/systemSettings';

interface ThemeToggleProps {
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { theme, setTheme } = useTheme();
  const {editOrAddSystemSetting} = useSystemSettings();

  const toggleTheme = () => {
    if (theme === Theme.LIGHT) {
      setTheme(Theme.DARK);
    } else if (theme === Theme.DARK) {
      setTheme(Theme.SYSTEM);
    } else {
      setTheme(Theme.LIGHT);
    }

    editOrAddSystemSetting({'theme': theme === Theme.LIGHT ? Theme.DARK : Theme.LIGHT});
  };

  return (
    <Button
      variant="ghost"
      onClick={toggleTheme}
      aria-label="Toggle theme"
      className={`
        p-2 h-10 w-10 rounded-xl
        bg-primary-10/50 dark:bg-primary-900/20
        border border-border/20
        text-foreground hover:text-primary
        hover:bg-primary-20 dark:hover:bg-primary-900/30
        hover:border-primary-300 dark:hover:border-primary-700
        transition-all duration-300 ease-in-out
        hover:scale-105 active:scale-95
        shadow-sm hover:shadow-md
        ${className}
      `}
    >
      {theme === Theme.LIGHT && <Sun className="h-5 w-5 text-amber-500" />}
      {theme === Theme.DARK && <Moon className="h-5 w-5 text-blue-400" />}
      {theme === Theme.SYSTEM && <Laptop className="h-5 w-5 text-purple-500" />}
    </Button>
  );
};

export default ThemeToggle;
