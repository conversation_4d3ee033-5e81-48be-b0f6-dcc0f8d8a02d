import React from "react";
import View from "../view";
import Text from "../text";
import { ArchiveX } from "lucide-react";
import BouncingLoader from "../BouncingLoader";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "./table";

interface DynamicTableProps {
  tableData: any[];
  tableHeaders: string[];
  isLoading?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  renderCell?: (
    rowIndex: number,
    colIndex: number,
    value: any
  ) => React.ReactNode;
  getRowKey?: (row: any, rowIndex: number) => string | number;
  onRowClick?: (row: any, rowIndex: number) => void;
  header?: {
    search?: React.ReactNode;
    sort?: React.ReactNode;
    filter?: React.ReactNode;
    action?: React.ReactNode;
  };
  footer?: {
    pagination?: React.ReactNode;
  };
}

const DynamicTable: React.FC<DynamicTableProps> = ({
  tableData,
  tableHeaders,
  isLoading = false,
  emptyMessage = "No Data Found!",
  emptyIcon = (
    <ArchiveX className="w-10 h-10 mx-auto mb-2 bg-primary-100 p-2 rounded-full text-primary" />
  ),
  renderCell,
  getRowKey,
  onRowClick,
  header,
  footer,
}) => {
  return (
    <View>
      {/* Header controls */}
      <View className="p-6 border-b border-border/20 bg-gradient-to-r from-primary-10/30 to-secondary-10/30 dark:from-primary-900/10 dark:to-secondary-900/10 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center backdrop-blur-sm">
        <View className="flex gap-3 w-full justify-between items-center">
          <View className="flex-1 max-w-md">
            {header?.search}
          </View>
          <View className="flex gap-3 items-center">
            {header?.sort}
            {header?.filter}
            {header?.action && (
              <View className="shrink-0">{header.action}</View>
            )}
          </View>
        </View>
      </View>

      {/* Table */}
      <View className="overflow-x-auto">
        <Table className="w-full min-w-max">
          <TableHeader>
            <TableRow className="bg-neutral-100 font-bold border-b border-neutral-200 dark:bg-background dark:border-none">
              {tableHeaders.map((header, index) => (
                <TableHead
                  key={index}
                  className={`py-3 px-4 text-sm text-text-light dark:text-gray-400"}`}
                  style={{ fontWeight: "bold" }}
                >
                  {header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={tableHeaders.length}
                  className="py-4 text-center relative"
                >
                  <View className="w-full z-50">
                    <BouncingLoader notFixed isLoading={isLoading} />
                  </View>
                </TableCell>
              </TableRow>
            ) : !tableData || tableData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={tableHeaders.length}
                  className="py-6 px-4 text-center text-text-light"
                >
                  {emptyIcon}
                  <Text as="span">{emptyMessage}</Text>
                </TableCell>
              </TableRow>
            ) : (
              tableData.map((row, rowIndex) => (
                <TableRow
                  key={getRowKey ? getRowKey(row, rowIndex) : rowIndex}
                  onClick={() => onRowClick?.(row, rowIndex)}
                  className={`
                    border-b border-border/10
                    hover:bg-primary-10/50 dark:hover:bg-primary-900/10
                    transition-all duration-200 ease-in-out
                    group
                    ${onRowClick ? "cursor-pointer hover:shadow-sm" : ""}
                  `}
                >
                  {Array.isArray(row) &&
                    row.map((cell, colIndex) => (
                      <TableCell
                        key={"td_" + colIndex}
                        className="py-4 px-6 whitespace-nowrap text-sm font-medium text-foreground group-hover:text-primary-600 transition-colors duration-200"
                      >
                        {renderCell
                          ? renderCell(rowIndex, colIndex, cell)
                          : cell}
                      </TableCell>
                    ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </View>

      {/* Footer (pagination) */}
      {footer?.pagination && <View className="pt-4">{footer.pagination}</View>}
    </View>
  );
};

export default DynamicTable;
