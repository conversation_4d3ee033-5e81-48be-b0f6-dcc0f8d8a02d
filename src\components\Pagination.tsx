import React from "react";
import Button from "./button";
import View from "./view";

interface PaginationProps {
  last_page?: number;
  current_page?: number;
  getPageNumberHandler?: (pageNuber: number) => void;
}

const PaginationComponent: React.FC<PaginationProps> = ({
  last_page = 1,
  current_page = 1,
  getPageNumberHandler,
}) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= last_page && getPageNumberHandler) {
      getPageNumberHandler(page);
    }
  };

  // Show max 5 page numbers with ellipsis
  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, current_page - delta); i <= Math.min(last_page - 1, current_page + delta); i++) {
      range.push(i);
    }

    if (current_page - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (current_page + delta < last_page - 1) {
      rangeWithDots.push('...', last_page);
    } else if (last_page > 1) {
      rangeWithDots.push(last_page);
    }

    return rangeWithDots;
  };

  return (
    <View className="flex items-center justify-between py-4 px-2">
      <View className="text-sm text-muted-foreground">
        Showing page {current_page} of {last_page}
      </View>

      <View className="flex items-center gap-1">
        <Button
          variant="outline"
          size="small"
          disabled={current_page === 1}
          onPress={() => handlePageChange(current_page - 1)}
          className="px-3 py-2 text-sm"
        >
          Previous
        </Button>

        {last_page <= 7 ? (
          // Show all pages if 7 or fewer
          [...Array(last_page)].map((_, index) => {
            const pageNum = index + 1;
            return (
              <Button
                key={pageNum}
                variant={pageNum === current_page ? "primary" : "ghost"}
                size="small"
                onPress={() => handlePageChange(pageNum)}
                className={`px-3 py-2 text-sm min-w-[40px] ${
                  pageNum === current_page
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "hover:bg-primary-10 dark:hover:bg-primary-900/20"
                }`}
              >
                {pageNum}
              </Button>
            );
          })
        ) : (
          // Show pages with ellipsis
          getVisiblePages().map((page, index) => (
            <Button
              key={index}
              variant={page === current_page ? "primary" : "ghost"}
              size="small"
              disabled={page === '...'}
              onPress={() => typeof page === 'number' ? handlePageChange(page) : undefined}
              className={`px-3 py-2 text-sm min-w-[40px] ${
                page === current_page
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : page === '...'
                    ? "cursor-default hover:bg-transparent"
                    : "hover:bg-primary-10 dark:hover:bg-primary-900/20"
              }`}
            >
              {page}
            </Button>
          ))
        )}

        <Button
          variant="outline"
          size="small"
          disabled={current_page === last_page}
          onPress={() => handlePageChange(current_page + 1)}
          className="px-3 py-2 text-sm"
        >
          Next
        </Button>
      </View>
    </View>
  );
};

export default PaginationComponent;
